////// GLOBAL //////


//// FUNCTIONS ////

// Split Section Spacing //
  .fluid-engine:has(#full-width) {
    --sqs-site-max-width:100vw;
    --sqs-site-gutter:0vw;
  }

// Smooth Scroll //
html {
  scroll-behavior: smooth;
}

* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Overflow Fix //

body {
  overflow-x: hidden;
}

//// COLOURS ////

:root { 
  --lightest: #FFFFFF !important; 
  --light: #F5F5F5 !important; 
  --lighttwo: #D0D0D0 !important;
  --bright: #6A9DB2 !important;
  --brighttwo: #6A9DB2 !important;
  --dark: #1C3D68 !important;
  --darktwo: #474747 !important;
  --darkthree: #626262 !important; 
  --darkfour: #1B3C68 !important;
  --darkest: #1F1F1F !important; 
  --grid-col-width: calc((100vw-12vw)/24)
}

//// FONTS ////

@font-face {
  font-family: Inter;
  src: url(https://static1.squarespace.com/static/66e424fb758f2c16ca943091/t/66e42577f5f7f3505b9d4371/1726227832293/Inter-VariableFont_opsz%2Cwght.ttf);
}

@font-face {
  font-family: 'Source Sans Pro';
  src: url(https://static1.squarespace.com/static/66e424fb758f2c16ca943091/t/675834f55a244f6ea084b6d9/1733833973884/sourcesanspro-regular-webfont.woff2);
}

@font-face {
  font-family: 'Source Sans Pro';
  font-weight: bold;
  src: url(https://static1.squarespace.com/static/66e424fb758f2c16ca943091/t/6758351b4924794ffb2253ce/1733834011857/sourcesanspro-semibold-webfont.woff2);
}

p strong em {
  background: var(--lightest);
  font-style: normal !important;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
}

//// HEADER ////

.wm-subnav-header img {
  min-height: 145px !important;
  width: auto !important;
}

.header-nav-item a {
  transition: all 250ms ease;
  color: var(--dark) !important;
}

.header-nav-item a:hover {
  color: var(--bright);
}


// Mobile Menu //

.header-menu-nav {
  border-top: solid 1px var(--lighttwo);
}

.header-menu-nav-item {
  margin-bottom: 1rem;
  text-align: left !important;
}

.header-menu-nav-item a {
  font-size: 1.5rem;
  font-weight: 400;
  text-align: left !important;
  margin: 0;
}

.header-menu-nav-item-content {
  align-items: center;
  .chevron {
    margin-top: 0 !important;
    margin-left: 0.5rem !important;
  }
}

.header-menu-nav-folder-content {
  padding-top: 4rem;
  padding-bottom: 0px !important;
  justify-content: flex-start;
}

.header-menu-controls {
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}

.header--menu-open .header .header-dropdown-icon svg {
    fill: var(--dark) !important;
    stroke: var(--dark) !important;
}

#wm-subnav .wm-secondary-container {
  max-width: 1228px !important;
  align-items: flex-end;
}
#wm-subnav .wm-social-icon-container {
  margin-bottom: 28px;
}
#wm-subnav .wm-subnav-cta-container {
  margin-bottom: 28px;
  margin-right: 20px;
}
.wm-subnav-cta-container { 
  margin-left: 0 !important;
  margin-top: 1rem;
}

.wm-subnav-cta {
  padding-left: 6vw;
  padding-right: 6vw;
  display: flex;
}

.wm-subnav-cta-container .wm-subnav-cta .sqs-button-element--primary:link,
.wm-subnav-cta-container .wm-subnav-cta .sqs-button-element--primary:visited {
  border: 1px solid #1B3C68 !important;
  background-color: white;
  color: #1B3C68;
}
.wm-subnav-cta-container .wm-subnav-cta .sqs-button-element--primary:hover,
.wm-subnav-cta-container .wm-subnav-cta .sqs-button-element--primary:active {
  border: 1px solid #1B3C68 !important;
  background-color: #1B3C68;
  color: white;
}

.wm-menu-subnav-item:nth-child(2) {
  display: none;
}

.wm-menu-subnav-item a {
  margin: 0 !important;
  font-size: 1.5rem !important;
}

.header-menu-bg {
  background: var(--lightest) !important;
}

.header-menu-controls-control {
  color: var(--darkest) !important;
  margin-top: 1rem !important;
}

@media (max-width: 768px) {
  .sqs-announcement-bar {
    background: var(--dark);
    p {
      color: var(--lightest) !important;
    }
  }
}

.top-bun, .patty, .bottom-bun {
  background: #1B3C68 !important;
}

// Mega Menu Pages //

.header-menu-controls-control {
  font-size: 1.125rem !important;
  padding-left: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 1rem !important;
}

section:has(#mega-menu-page) {
  @media (max-width: 768px) {
    .section-background {
      background: var(--lightest) !important;
    }
    .sqsrte-large {
      font-size: 1.4rem !important;
    }
    p {
      font-size: 1.25rem !important;
    }
    .sqsrte-small {
      font-size: 1rem !important;
    }
    p {
      @media (max-width:768px) {
        color: var(--dark) !important;
        margin-bottom: 0.5rem !important;
        margin-top: 0 !important;
        line-height: 1.5em !important;
        strong {
          font-weight: 400 !important;
        }
      }
    }
    p a {
      color: var(--darkest) !important;
      @media (max-width: 768px) {
        color: var(--dark) !important;
      }
    }
  }
  .sqsrte-large {
    font-size: 1.05em;
  }
  p {
    font-size: 0.95em
  }
  p a {
    text-decoration: none;
    transition: all 250ms ease;
  }
  p a:hover {
    color: var(--bright) !important;
  }
  ul li p::before {
    display: none;
  }
  a strong {
    margin-left: 0 !important;
  }
}

section[data-section-id="67d2e6a7b1ec6e5e565d5981"] {
  @media (max-width: 768px) {
    p:not(.sqsrte-large) a strong {
      margin-left: 1rem;
    }
    li p a {
      margin-left: 2rem;
    }
  }
}

/* Different Width Mega Menus */

[data-reference-url="/policy-solutions-mega-menu"] {
  --mega-menu-max-width: 600px;
  overflow: hidden !important;
} 

[data-reference-url="/policy-explainers-mega-menu"] {
  --mega-menu-max-width: 300px;
  overflow: hidden !important;
}

[data-reference-url="/faq-glossary-mega-menu"] {
  --mega-menu-max-width: 110px;
  overflow: hidden !important;
}

//Customize Scroll bar
[data-reference-url] {
  scrollbar-width: thin;
  scrollbar-color: #888 transparent;

  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0%);
  }
  &::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
  }
}

// Announcement bar //
.sqs-announcement-bar-dropzone {
}
.sqs-announcement-bar-dropzone
a.sqs-announcement-bar-url:hover,
.sqs-announcement-bar-dropzone
a.sqs-announcement-bar-url:active {
  box-shadow: inset 0 0 100px 100px rgba(255, 255, 255, 0.06);
}
.sqs-announcement-bar-dropzone #announcement-bar-text-inner-id strong {
  color: #9bd3dd;
}
.sqs-announcement-bar-close {
  display: none !important;
}

.sqs-announcement-bar-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  p {
    font-size: 1rem !important;
     /* kerning adjustment */
    letter-spacing: 0.03em !important; 
  }
}

// Adjusting main header //

.header-announcement-bar-wrapper {
  padding-top: 23px !important;
  padding-bottom: 23px !important;
}

@media (min-width: 768px) {
.header-title {
  display: none;
}


.header-title-nav-wrapper {
  flex-basis: 100% !important;
}
}

@media (min-width:769px) {
  .header-display-mobile {
    display: none !important;
  }
  .header-display-desktop {
    display: flex !important;
  }
  .header-burger {
   display: none !important;
  }
  .header-nav {
    display: block !important;
  }
}

// Secondary Nav //

@media (max-width: 768px) {
  #wm-subnav {
    display: none !important;
  }
}

#wm-subnav {
  padding-left: var(--sqs-site-gutter);
  padding-right: var(--sqs-site-gutter);
  margin: 0 auto !important;
}

.wm-secondary-sticky-wrapper {
  max-width: var(--sqs-site-max-width) !important;
  margin: 0 auto;
}

.header-nav-list {
  display: flex;
  margin: 0 auto !important;
  max-width: var(--sqs-site-max-width);
}

@media (min-width: 768px) {
.wm-subnav-cta-container {
  margin-top: 0 !important;
}

.wm-subnav-cta {
  font-size: 1.2rem !important;
  padding-right: 0 !important;
}
  
 .wm-subnav-cta a {
  font-size: 1.2rem !important;
}
}

.wm-secondary-container {
  padding: 0rem !important;
}

.wm-subnav-header img {
  height: 100px !important;
}

.wm-subnav-nav-list {
  display: flex;
  gap: 0.7rem;
  margin-right: 1rem !important;
}

.wm-subnav-item {
  margin-right: 0 !important;
}

.wm-subnav-item a {
  color: var(--darkthree) !important;
  font-weight: 400 !important;
  font-size: 1.2rem;
}

.wm-social-icon-container {
  gap: .5rem;
  a::after {
    content: "";
    display: inline-block;
  }
  a {
    margin-left: 0 !important;
    display: inline-block !important;
    width: 18px !important;
    font-size: 1.2rem;
  }
}

.wm-social-icon-container circle, .wm-social-icon-container path, .wm-social-icon-container svg {
  stroke: var(--dark) !important;
  fill: var(--dark) !important;
}


.wm-subnav-cta {
  padding-left: 1.5rem !important;
}

.wm-subnav-cta a.sqs-button-element--primary {
  background: var(--dark);
  color: var(--lightest);
  border: solid 1px var(--dark) !important;
}

#wm-subnav {
  border-bottom: solid 1px var(--lighttwo);
}

#wm-subnav .wm-secondary-container {
  padding-top: 1rem;
  padding-bottom: 1rem;
  padding-left: 0;
  padding-right: 0;
  max-width: 1440px;
  margin: 0 auto;
}

/* Accessible "visually hidden" helper for the label */
.visually-hidden {
  position:absolute !important; width:1px; height:1px; padding:0; margin:-1px;
  overflow:hidden; clip:rect(0,0,0,0); white-space:nowrap; border:0;
}

/* Layout and spacing in the secondary nav */
#wm-subnav .wm-social-icon-container { display:flex; align-items:center; }
#wm-subnav .wm-subnav-search { display:flex; align-items:center; gap:.5rem; margin-right:.5rem; }

/* SEARCH Input styling */
#wm-subnav .wm-subnav-search input[type="search"] {
  height: 34px;
  padding: 0 .75rem;
  border: 1px solid var(--lighttwo);
  border-radius: 999px;
  font-size: 1.2rem;
  color: var(--darkest);
  width: 140px;
  transition: width .2s ease, border-color .2s ease, box-shadow .2s ease;
}

#wm-subnav .wm-subnav-search input::placeholder { color: 666; }

#wm-subnav .wm-subnav-search input:focus {
  outline: none;
  border-color: var(--brighttwo);
  box-shadow: 0 0 0 2px rgba(106,157,178,0.2);
  width: 220px;
}

/* Submit button (magnifier) */
#wm-subnav .wm-subnav-search .icon {
  display:inline-flex; align-items:center; justify-content:center;
  width: 42px; 
  height: 42px;
  border: 1px solid var(--lighttwo);
  border-radius: 999px;
  background: transparent;
  cursor: pointer;
  color: var(--dark);
}

#wm-subnav .wm-subnav-search .icon:hover { opacity: .8; }

#wm-subnav .wm-subnav-search svg { 
  width: 24px; 
  height: 24px; 
}

/* Ensure icon strokes are strokes (not filled) */
#wm-subnav .wm-subnav-search path,
#wm-subnav .wm-subnav-search circle { fill:none; stroke: currentColor; stroke-width:2px; }


#wm-subnav .wm-social-icon-container .wm-subnav-search path,
#wm-subnav .wm-social-icon-container .wm-subnav-search circle {
  fill: none !important;
  stroke: currentColor !important;
  stroke-width: 2px !important;
}

/* Magnifier thicker + icon on the left + underline-style input */
#wm-subnav .wm-subnav-search { display:flex; align-items:center; gap:.5rem; }

/* Put the icon before the input and make it look like a plain icon */
#wm-subnav .wm-subnav-search .icon {
  order: 0;
  border: none;
  width: auto; height: auto;
  padding: 0;
  background: transparent;
  cursor: pointer;
}

/* Underline input (no pill) */
#wm-subnav .wm-subnav-search input[type="search"] {
  order: 1;
  width: 180px; height: 24px;
  padding: 4px .25rem 4px .25rem;
  border: none;
  border-bottom: 2px solid var(--lighttwo);
  border-radius: 0;
  background: transparent;
  font-size: 1.1rem;
  color: var(--darkest);
  transition: width .2s ease, border-color .2s ease;
}
#wm-subnav .wm-subnav-search input::placeholder { color: var(--darkthree); }
#wm-subnav .wm-subnav-search input:focus {
  outline: none;
  border-bottom-color: var(--brighttwo);
  width: 260px;
}

/* Icon size */
#wm-subnav .wm-subnav-search svg { width: 24px; height: 24px; }

/* Keep magnifier stroked (not filled) and make it thicker */
#wm-subnav .wm-social-icon-container .wm-subnav-search path,
#wm-subnav .wm-social-icon-container .wm-subnav-search circle {
  fill: none !important;
  stroke: currentColor !important;
  stroke-width: 3px !important; /* thicker */
}

/* Secondary nav — hide WM social icons but keep the container for search */
#wm-subnav .wm-social-icon-container a[data-wm-social] {
  display: none !important;
}

/* Optional: close up any leftover space */
#wm-subnav .wm-social-icon-container {
  gap: 0 !important;   /* or .5rem if you like a little space before the search */
}


// Dropdowns //

/**
* Hide First Item in Dropdowns
* For ALL Dropdowns
**/
#header .replaced-folder {
  & + .header-nav-folder-content > *:first-child{
    display: none;
  }
}

.header-nav-item--folder:after {
  position: absolute;
    top: .66rem;
    right: 0;
    content: "";
    display: inline-block;
    margin-left: 0.25rem;
    width:0.66rem;
    height: 0.66rem;
    background: url(https://static1.squarespace.com/static/66e424fb758f2c16ca943091/t/671780a470c4897ffa8c3cdb/1729593508563/icon+%281%29+1.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: 50%;
  transition: all 350ms ease;
}

.header-nav-item--folder:hover:after {
  transform: translateY(2px);
}

.header-nav-folder-content {
  min-width: none !important;
}

.header-nav-item--folder {
  z-index: 999;
  position: relative;
  padding-right: 1rem;
  .header-nav-folder-content {
    transition: all 350ms ease;
    padding: 0.25rem !important;
    background: var(--dark);
  }
  .header-nav-folder-item {
    padding: 0.25rem 0.75rem !important;
    transition: all 350ms ease;
    a {
    color: var(--lightest) !important;
      font-weight: 400 !important;
      font-size: 1rem;
    }
  }
  .header-nav-folder-item:hover {
  }
  .header-nav-folder-item:hover a {
    opacity: 1 !important;
  }
}

// Remove Active Link Underline //
.header-nav-item--active a {
  background-image: none !important;
}

.header-nav-folder-title {
  text-decoration: none !important;
}

.header-nav-folder-title-text {
  background: none !important;
}

.header-nav-folder-item-content {
  background: none !important;
}

// Add Link Hover Effect //
.header-nav-item a {
  transition: 350ms ease;
}
.header-nav-item a:hover {
  opacity: 0.7 !important;
}

// Reduce Social Icon Spacing //
.header-actions--right .header-actions-action--social .icon:not(:first-child) {
  margin-left: 12px !important;
}

//// FOOTER ////

#footer-sections a {
  transition: 350ms ease;
  text-decoration: none;
}

#footer-sections a:hover {
  opacity: 0.7 !important;
}

@media (max-width: 768px) {
  #block-yui_3_17_2_1_1733228518299_91045 {
    .sqs-block-alignment-wrapper {
      justify-content: center;
    }
  }
  #block-yui_3_17_2_1_1733228518299_140928 {
    .sqs-svg-icon--list {
      justify-content: center;
    }
  }
}

#block-d765aaf116a2b9ba3eea {
  .sqsrte-large {
    font-size: 1.5rem !important;
  }
}

//// BLOCK SPECIFIC ////
// Quote block //
blockquote {
  border-left: solid 2px var(--darkfour) !important;
  margin-top: 18px !important;
  padding-left: 10px !important;
  font-size: 17.5px !important;
}
// ARCHIVE BLOCK //

// ACCORDION BLOCK //
.sqs-block-accordion .accordion-item__click-target {
  border-bottom: solid 1px var(--darkest);
}

// BUTTON BLOCK //

.sqs-button-element--secondary {
  border: solid 1px var(--lighttwo) !important;
}

/* Homepage — make ALL content-area (not header/footer) buttons 1.25rem */
body.homepage #page .fluid-engine a.sqs-block-button-element {
  font-size: 1.25rem !important;
  letter-spacing: 0.025em !important;
  font-weight: 400 !important;
}


// CODE BLOCK //

// EMBED BLOCK //

// SHAPE BLOCK //

.sqs-shape-block-container[data-shape-name="rabbet"] {
  width: 1px;
  height: 100%;
  margin: 0 auto;
  background: var(--lighttwo);
  svg {
    visibility: hidden;
  }
}

// FORM BLOCK //

// Remove Focus //
:focus {
  outline: 0px !important;
}

// GALLERY BLOCK //

// IMAGE BLOCK //

// TABLE OF CONTENTS //

html {
  scroll-behavior: smooth;
}

/* Styling for the words "Table of Contents" */
.table-of-contents p{
  font-size: 1.5rem;
  font-weight: 600;
}

/* Adjust font size of the links (change to the px size you want) */
.table-of-contents li a{
  font-size: inherit !important;
}

.table-of-contents li {
  margin: 0.2rem 0rem !important;
}

.table-of-contents ul {
  border-left: solid 1px var(--lighttwo);
  list-style: none !important;
}

/* Giving the table of contents a background color, rounding the background corners, and giving it some padding */
.table-of-contents{
  background-color: var(--light);
  border: solid 1px var(--lighttwo);
  padding: 3rem 4rem;
}

// MARKDOWN BLOCK //

// NEWSLETTER BLOCK //

#block-yui_3_17_2_1_1726227723244_13818 {
  .newsletter-form-name-fieldset.name {
    display: flex;
    max-width: 100% !important;
    gap: 0.5rem;
    .newsletter-form-field-wrapper {
      flex-basis: 50%;
      max-width: 50% !important;
      min-width: 0 !important;
    }
  }
  .newsletter-form-header-title {
    font-size: 1.125rem !important;
  }
  .newsletter-form-fields-wrapper, .newsletter-form-button-wrapper {
    margin-top: 0 !important;
  }
  .newsletter-form-body {
    padding-bottom: 0 !important;
    display: flex !important;
    flex-direction: column;
  }
  .newsletter-form-button-wrapper {
    button {
      padding: 0.75rem 1.5rem !important;
    }
  }
  .newsletter-form-fields-wrapper {
    flex-grow: 1 !important;
    margin-right: 1rem !important;
  }
  .newsletter-form-field-wrapper {
    width: 100% !important;
    input {
      padding: 0.75rem 1.5rem !important;
      border-radius: 0.2rem !important;
      border: none !important;
      }
    input::placeholder {
      color: var(--darkest) !important;
    }
    }
  }

// QUOTE BLOCK //

// SCROLLING BLOCK //

// SOCIAL BLOCK //

// SUMMARY BLOCK //

// BREADCRUMBS //

#breadcrumbs a, #breadcrumbs p {
  font-size: 0.875rem;
}

#breadcrumbs span {
  font-size: 1rem;
}

#breadcrumbs a {
  font-weight: 400 !important;
  text-decoration: underline;
  color: var(--darkfour) !important;
}

//// GLOSSARY ////
.jDEBjs.glossary-container {
  width: 100% !important;
}

// POLICY EXPLAINER LINK //

section:has(#explainer-button) {
  .sqs-background-enabled a {
    display: inline-block !important;
    padding: 0.75rem 1.5rem !important;
    border: solid 1px var(--lighttwo) !important;
    border-radius: 0.25rem !important;
    color: var(--darkest);
    text-decoration: none;
    margin-top: 0.5rem !important;
  }
}

//// SECTION SPECIFIC ////

// AUTO-LAYOUT SECTION //

// MODEL POLICY SUMMARY BLOCKS //

.summary-content {
  background: var(--light);
  border: solid 1px var(--lighttwo);
  padding: 3rem;
  position: relative;
  transition: all 250ms ease;
}

.summary-content:hover {
  border: solid 1px var(--brighttwo) !important;
}

.summary-metadata-container {
  margin-bottom: 2% !important;
}

.summary-metadata-item a {
  background: var(--lightest);
  font-size: 1rem !important;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  color: var(--darkest) !important;
}

.summary-metadata-item {
  opacity: 1 !important;
  color: var(--light);
  pointer-events: none !important;
}

.summary-title {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin-bottom: 1rem !important;
}

.summary-excerpt {
  display: inline-block !important;
  margin-right: 16.5rem !important;
}

.summary-excerpt p {
  font-size: 1rem !important;
}

a.summary-read-more-link {
  display: flex !important;
  align-items: center;
  gap: 0.75rem;
  position: absolute;
  right: 3rem;
  bottom: 3rem;
  font-size: 1rem !important;
}

.summary-read-more-link::after {
    content: "";
      display: block;
      width: 0.75rem;
      height: 0.75rem;
      background: url(https://static1.squarespace.com/static/66e424fb758f2c16ca943091/t/66e43afd91daf96bdf6092d9/1726233341198/icon.png);
      background-size: contain;
      background-position: 50%;
      background-repeat: no-repeat;
  }

// GALLERY SECTION //

////// ON PAGE //////

//HOMEPAGE hero 2nd line //

#block-yui_3_17_2_1_1747262748593_527689 h3 {
  font-family: 'Inter', sans-serif !important;
  font-size: 1.6rem !important;
  line-height: 1.4 !important;
  font-weight: 400;
}

// STATE DATA DASHBOARDS //

@media (max-width: 768px) {
  #block-2f06d430c10daa93b0c5, #block-yui_3_17_2_1_1742247426094_3852 {
    display: none;
  }
}

@media (min-width:768px) {
  #block-ab8261476d838a03a7e0, #block-b981f4e6648b94647d90 {
    display: none;
  }
}

// SOLUTIONS IN ACTION //

@media (max-width: 768px) {
  #block-yui_3_17_2_1_1745256571383_4563, #block-yui_3_17_2_1_1745256571383_5025 {
    display: none;
  }
}

@media (min-width:768px) {
  #block-611821f73d437f9932cf, #block-1f53d0820663ef0e757a {
    display: none;
  }
}

// POLICY PAGES //

// Sidebars //

div[style*="backdrop-filter: blur(15px)"] {
  @media(max-width: 768px) {
    display: none;
  }
  a {
    display: block;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border-bottom: solid 1px var(--lighttwo);
    color: var(--darkest);
    font-size: 0.95rem;
  }
  a:hover {
    color: var(--dark) !important;
  }
  .sqs-block-content {
  padding-right: 3vw !important;
  }
  p, ul {
    margin: 0 !important;
  }
  li > ul {
    border-left: solid 3px var(--darkest);
  }
  ul {
    border-left: solid 3px var(--dark);
    list-style-type: none !important;
    padding-left: 0rem;
    margin-left: 1rem !important;
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  li p::before {
    display: none !important;
  }
  p:has(em) {
    background: var(--light);
    border-left: solid 3px var(--dark);
    em {
    font-style: normal !important;
    font-weight: bold;
    color: var(--dark);
    }
  }
  p:has(strong) {
    a {
    background: var(--dark) !important;
    color: var(--lightest) !important;
    }
    strong {
      font-weight: bold;
    }
  }
  p:has(strong) a:hover {
    background: var(--dark);
  }
  a {
    text-decoration: none;
  }
}

// BLOG PAGES //

.blog-item-meta-wrapper {
  flex-direction: column;
  align-items: flex-start !important;
  row-gap: 4rem !important;
}

.blog-item-category {
  background: var(--darkest);
  color: var(--lightest) !important;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-block;
  pointer-events: none !important;
}

.blog-item-author-date-wrapper {
  flex-direction: column;
  gap: 0.5rem;
}

.blog-item-top-wrapper {
  margin-bottom: 4rem !important;
}

/*
.blog-item-wrapper {
  background: url(https://static1.squarespace.com/static/66e424fb758f2c16ca943091/t/66e447277eda276ef9c965a1/1726236456169/pattern-randomized+%282%29+2.png);
  background-size: 50%;
  background-repeat: no-repeat;
  background-position: 100% 0%;
}
*/

.item-pagination {
  display: none !important;
}

// HOW TO USE //

// Line Blocks //

section[data-section-id="66f54c58cbcd5e0c2d62421c"] {
  .sqs-shape-rectangle {
    width: 4px !important;
  }
}

  // Post Submit form color change //
.form-submission-text p {
    color: #1B3C68 !important;
}

.form-submission-text strong {
    color: #1B3C68 !important;
}
.post-submit-text {
  	margin-top: -20px;
  	text-align: center;
    color: #1B3C68 !important;
}
.post-submit-text-bold {
	font-weight: 900;
}

// STATE POLICY HIGHLIGHTS POSTS //

// Remove underline from linked text in policy highlights //
a[href^="/policy-priorities/"] [class^="sqsrte-text-color--"] {
  text-decoration: none;
}

.blog-item-wrapper:has(#state-policy-highlight-post) {
  article.entry {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }
  .blog-item-comments {
    display: none !important;
  }
  .row {
    max-width: 100% !important;
    margin-left: 0;
  }
  .sqs-html-content > ul li:nth-last-child(1) {
    margin-bottom: 0 !important;
    p {
      margin-bottom: 0 !important;
    }
  }
  .sqs-block-html.sqs-background-enabled {
    border-left: solid 5px #1B3C68 !important;
    h2 {
      margin-top: 0 !important;
    }
    p {
      margin-bottom: 0 !important;
      margin-top: 10px !important;
    }
  }
  .sqs-html-content ul {
    padding-left: 20px;
    margin-top: 5px;
  }
  .sqs-html-content p + ul {
    margin-top: -5px;
  }
  .sqs-html-content li {
    margin-bottom: 12px;
  }
  .sqs-block-html:not(.sqs-background-enabled), .sqs-block-code {
   	padding-left: 0;
    padding-right: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  .blog-item-top-wrapper {
    margin-bottom: 35px !important;
  }
  .blog-item-title {
    border-bottom: solid 2px #1B3C68;
    padding-bottom: 0px;
    h1 {
      font-size: 36px;
      font-weight: 700 !important;
    }
  }
  .sqs-block-spacer {
    height: 30px;
    padding: 0;
    box-sizing: border-box;
  }
  .background-no-border {
    background: rgba(90,141,170,0.15);
    padding: 15px;
    border-radius: 4px;
    h2 {
      margin-top: 0 !important;
    }
    p {
      margin-bottom: 0 !important;
      margin-top: 10px !important;
    }
    p strong {
      color: #1B3C68;
    }
    ul {
      margin-bottom: 0 !important;
    }
    li a {
      font-weight: 600;
    }
  }
  .sqs-html-content li {
    margin-bottom: 0px !important;
  }
  li p::before {
    font-size: 1.5em;
    font-weight: 900;
    position: relative;
    top: 1px
  }
  li li p::before {
    font-size: 1.25em !important;
  }
  li p strong {
    font-size: 1.1em;
  }
  h2 {
    font-size: 26px;
    margin-bottom: 10px;
  }
  p {
    margin-top: 5px;
  }
  p a:hover {
    text-decoration: underline;
  }
  @media (max-width: 768px) {
    .blog-item-top-wrapper {
      margin-bottom: 0 !important;
    }
    .sqs-block-html {
      margin-top: 3rem !important;
      margin-bottom: 3rem !important;
    }
     ul {
      margin-left: 0.5rem !important;
    }
    li p:before {
      font-size: 1rem !important;
    }
  }
}

.wm-mega-menu[data-layout=inset].open .mega-menu-arrow {
    background-color: #1B3C68 !important;
}

/* Style summary titles ONLY on the Explainers section */
.fe-6838c2388bc9b94f79ee2d7a .summary-title a {
  color: #1B3C68 !important; /* Your dark blue */
  font-family: 'Libre Baskerville', serif !important;
  font-size: 1.5rem !important;  /* Approx Title 2 */
  font-weight: 700;
  line-height: 1.3;
  text-decoration: none;
}

/* Add hover effect to those titles */
.fe-6838c2388bc9b94f79ee2d7a .summary-title a:hover {
  text-decoration: underline;
  color: #8B2635; /* Your brand burgundy */
}

/* Search Results */
/* Using the attribute selector [data-url=""] specifically for the mega-menu "page" in the results following the selectors definitions detailed at: https://www.w3schools.com/cssref/css_selectors.php
*/
.search-results div[data-url="/policy-solutions-mega-menu"] {
  outline: 1px solid red;
  display: none;
}
/* Selecting the image container inside the search results*/
.search-results .sqs-search-container-item .sqs-main-image-container {
  outline: 1px solid red;
  display: none;  
}

