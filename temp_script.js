<script>
  document.addEventListener("DOMContentLoaded", function() {    
    console.log('breadcrumbs');
    const title = document.querySelector("h1.entry-title");
    console.log('title:  ', title);
    if (title) {
      console.log('title:  ', title);
 
      const backDivider = document.createElement("span");
      backDivider.classList.add("breadcrumb-icon");
      backDivider.textContent = ">";
      backDivider.style.position = "relative";
      backDivider.style.top = "-1em";           // Only moves the link
      backDivider.style.color = "#666";
      backDivider.style.fontSize = "0.9em";
      backDivider.style.textDecoration = "none";
      backDivider.style.display = "inline-block";
      backDivider.style.marginBottom = "1em";
      console.log('backDivider:  ', backDivider);
  
      const SIALink = document.createElement("a");
      SIALink.href = "/solutions-in-action"; 
      SIALink.textContent = "Soutions In Action";
      SIALink.style.color = "#1b3c68";
      SIALink.style.fontSize = "0.9em";
      SIALink.style.textDecoration = "none";
      //SIALink.style.display = "inline-block";
      SIALink.style.marginBottom = "1em";
      console.log('SIALink:  ', SIALink);

      const homeLink = document.createElement("a");
      homeLink.href = "/"; 
      homeLink.textContent = "Home";
      homeLink.style.position = "relative";
      homeLink.style.top = "-1em";           // Only moves the link
      homeLink.style.color = "#1b3c68";
      homeLink.style.fontSize = "0.9em";
      homeLink.style.textDecoration = "none";
     // homeLink.style.display = "inline-block";
      homeLink.style.marginBottom = "1em";
      console.log('homeLink:  ', homeLink); 
   
      const backLink = document.createElement("span");
      //backLink.href = "/solutions-in-action"; // ← Update if needed
      backLink.textContent = title.innerHTML;
      backLink.style.position = "relative";
      backLink.style.top = "-1em";           // Only moves the link
      backLink.style.color = "#666";
      backLink.style.fontSize = "0.9em";
      backLink.style.textDecoration = "none";
      //backLink.style.display = "inline-block";
      backLink.style.marginBottom = "1em";
      console.log('backLink:  ', backLink);
     
      const comboLink = document.createElement("span");
      // this order matters
      comboLink.appendChild(homeLink);
      comboLink.appendChild(backDivider);
      comboLink.appendChild(SIALink);
      comboLink.appendChild(backDivider);
      comboLink.appendChild(backLink);
	  console.log('combolink:  ', comboLink);
      
      title.parentElement.insertBefore( comboLink, title );
    }
  });
</script>